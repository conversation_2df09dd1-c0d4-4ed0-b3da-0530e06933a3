from pyspark.sql import SparkSession
from pyspark.sql.functions import col, lower, regexp_extract_all, explode
import re

# Initialize Spark session
spark = SparkSession.builder \
    .appName("WordCount") \
    .getOrCreate()

# Define stop words to exclude
stop_words = {
    'this', 'that', 'with', 'from', 'have', 'been', 'will', 'just', 'they',
    'their', 'what', 'when', 'where', 'which', 'while', 'about', 'after',
    'before', 'during', 'through', 'under', 'over', 'between', 'into',
    'than', 'then', 'there', 'here', 'were', 'would', 'could', 'should',
    'these', 'those', 'them', 'some', 'more', 'most', 'other', 'such',
    'only', 'also', 'very', 'much', 'many', 'like', 'https', 'http'
}

# Broadcast stop words for efficient distributed access
stop_words_broadcast = spark.sparkContext.broadcast(stop_words)

# PySpark equivalent of your original code
def extract_and_filter_words(text):
    """Extract words and filter out stop words"""
    if text is None:
        return []
    
    # Extract words with 4+ characters (equivalent to re.findall(r'\b\w{4,}\b', text.lower()))
    words = re.findall(r'\b\w{4,}\b', text.lower())
    
    # Filter out stop words
    stop_words_set = stop_words_broadcast.value
    meaningful_words = [word for word in words if word not in stop_words_set]
    
    return meaningful_words

# Convert DataFrame to RDD for processing (most similar to your original approach)
text_rdd = df.select("text").rdd.map(lambda row: row.text).filter(lambda x: x is not None)

# Extract and count words
words_rdd = text_rdd.flatMap(extract_and_filter_words)
word_counts = words_rdd.map(lambda word: (word, 1)) \
    .reduceByKey(lambda a, b: a + b) \
    .sortBy(lambda x: x[1], ascending=False)

# Display top 20 most common words (equivalent to word_counts.most_common(20))
top_20_words = word_counts.take(20)
print(top_20_words)

# Alternative: More PySpark-native approach using DataFrame operations
# df_words = df.filter(col("text").isNotNull()) \
#     .select(explode(regexp_extract_all(lower(col("text")), r'\b\w{4,}\b')).alias("word")) \
#     .filter(~col("word").isin(list(stop_words))) \
#     .groupBy("word").count() \
#     .orderBy(col("count").desc()) \
#     .limit(20)
# 
# df_words.show()

# Don't forget to stop the Spark session when done
# spark.stop()
