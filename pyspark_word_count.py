from pyspark.sql import SparkSession
from pyspark.sql.functions import col, lower, regexp_extract_all, explode, filter as spark_filter
from pyspark.sql.types import ArrayType, StringType
import re

# Initialize Spark session
spark = SparkSession.builder \
    .appName("WordCount") \
    .config("spark.sql.adaptive.enabled", "true") \
    .config("spark.sql.adaptive.coalescePartitions.enabled", "true") \
    .getOrCreate()

# Define stop words to exclude
stop_words = {
    'this', 'that', 'with', 'from', 'have', 'been', 'will', 'just', 'they',
    'their', 'what', 'when', 'where', 'which', 'while', 'about', 'after',
    'before', 'during', 'through', 'under', 'over', 'between', 'into',
    'than', 'then', 'there', 'here', 'were', 'would', 'could', 'should',
    'these', 'those', 'them', 'some', 'more', 'most', 'other', 'such',
    'only', 'also', 'very', 'much', 'many', 'like', 'https', 'http'
}

# Broadcast the stop words set for efficient access across all nodes
stop_words_broadcast = spark.sparkContext.broadcast(stop_words)

# Assuming you have a DataFrame 'df' with a 'text' column
# For demonstration, let's create a sample DataFrame or load from your data
# df = spark.read.csv("path/to/your/data.csv", header=True, inferSchema=True)

# Method 1: Using DataFrame API with built-in functions
def word_count_dataframe_api(df):
    """
    Word count using PySpark DataFrame API with built-in functions
    """
    # Filter out null values and convert to lowercase
    df_clean = df.filter(col("text").isNotNull())
    
    # Extract words using regex (4+ characters) and convert to lowercase
    df_words = df_clean.select(
        explode(
            regexp_extract_all(lower(col("text")), r'\b\w{4,}\b')
        ).alias("word")
    )
    
    # Filter out stop words
    df_filtered = df_words.filter(
        ~col("word").isin(list(stop_words))
    )
    
    # Count words and get top 20
    word_counts = df_filtered.groupBy("word").count() \
        .orderBy(col("count").desc()) \
        .limit(20)
    
    return word_counts

# Method 2: Using RDD operations (more similar to your original approach)
def word_count_rdd_approach(df):
    """
    Word count using RDD operations for more control over the processing
    """
    def extract_and_filter_words(text):
        if text is None:
            return []
        
        # Extract words with 4+ characters
        words = re.findall(r'\b\w{4,}\b', text.lower())
        
        # Filter out stop words using the broadcasted set
        stop_words_set = stop_words_broadcast.value
        meaningful_words = [word for word in words if word not in stop_words_set]
        
        return meaningful_words
    
    # Convert to RDD and process
    text_rdd = df.select("text").rdd.map(lambda row: row.text)
    
    # Extract and filter words
    words_rdd = text_rdd.flatMap(extract_and_filter_words)
    
    # Count words
    word_counts_rdd = words_rdd.map(lambda word: (word, 1)) \
        .reduceByKey(lambda a, b: a + b) \
        .sortBy(lambda x: x[1], ascending=False)
    
    # Get top 20
    top_20_words = word_counts_rdd.take(20)
    
    return top_20_words

# Method 3: Hybrid approach - RDD for processing, DataFrame for final operations
def word_count_hybrid_approach(df):
    """
    Hybrid approach combining RDD processing with DataFrame operations
    """
    def process_text_row(row):
        text = row.text
        if text is None:
            return []
        
        # Extract words with 4+ characters
        words = re.findall(r'\b\w{4,}\b', text.lower())
        
        # Filter out stop words
        stop_words_set = stop_words_broadcast.value
        meaningful_words = [word for word in words if word not in stop_words_set]
        
        return [(word, 1) for word in meaningful_words]
    
    # Process using RDD
    word_pairs_rdd = df.rdd.flatMap(process_text_row)
    
    # Convert back to DataFrame for easier manipulation
    word_counts_df = spark.createDataFrame(word_pairs_rdd, ["word", "count"]) \
        .groupBy("word").sum("count") \
        .withColumnRenamed("sum(count)", "count") \
        .orderBy(col("count").desc()) \
        .limit(20)
    
    return word_counts_df

# Example usage:
if __name__ == "__main__":
    # Load your data (replace with your actual data path)
    # df = spark.read.csv("data/*.csv", header=True, inferSchema=True)
    
    # For demonstration, create a sample DataFrame
    sample_data = [
        ("This is a sample text with many words that should be counted properly",),
        ("Another text with different words and some repeated terms",),
        ("More text data for testing the word counting functionality",),
    ]
    df = spark.createDataFrame(sample_data, ["text"])
    
    print("Method 1 - DataFrame API:")
    result1 = word_count_dataframe_api(df)
    result1.show()
    
    print("\nMethod 2 - RDD Approach:")
    result2 = word_count_rdd_approach(df)
    for word, count in result2:
        print(f"{word}: {count}")
    
    print("\nMethod 3 - Hybrid Approach:")
    result3 = word_count_hybrid_approach(df)
    result3.show()
    
    # Stop Spark session
    spark.stop()
